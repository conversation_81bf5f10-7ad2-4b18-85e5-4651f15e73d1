-- Database schema for search engine
-- Run this script to initialize the database

-- Create database if it doesn't exist
-- Note: This needs to be run separately as a superuser
-- CREATE DATABASE inverted_index_db;

-- Connect to the database
-- \c inverted_index_db

-- Create tables
CREATE TABLE IF NOT EXISTS webpages (
    doc_id BIGSERIAL PRIMARY KEY,
    url TEXT UNIQUE NOT NULL,
    title TEXT,
    description TEXT,
    keywords TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS tokens (
    id SERIAL PRIMARY KEY,
    token_text TEXT UNIQUE NOT NULL
);

CREATE TABLE IF NOT EXISTS token_documents (
    token_id INTEGER NOT NULL REFERENCES tokens(id),
    doc_id BIGINT NOT NULL REFERENCES webpages(doc_id),
    tfidf_score DOUBLE PRECISION NOT NULL,
    PRIMARY KEY (token_id, doc_id)
);

-- <PERSON>reate indexes for performance
CREATE INDEX IF NOT EXISTS idx_webpages_url ON webpages(url);
CREATE INDEX IF NOT EXISTS idx_tokens_token_text ON tokens(token_text);
CREATE INDEX IF NOT EXISTS idx_token_documents_token_id ON token_documents(token_id);
CREATE INDEX IF NOT EXISTS idx_token_documents_doc_id ON token_documents(doc_id);
CREATE INDEX IF NOT EXISTS idx_token_documents_tfidf_score ON token_documents(tfidf_score DESC);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create a trigger to automatically update the updated_at column
CREATE TRIGGER update_webpages_updated_at
BEFORE UPDATE ON webpages
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();
