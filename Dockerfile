# Build stage
FROM golang:1.20-alpine AS builder

WORKDIR /app

# Copy go.mod and go.sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy the source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o search_engine

# Final stage
FROM alpine:latest

WORKDIR /app

# Install CA certificates for HTTPS requests
RUN apk --no-cache add ca-certificates

# Copy the binary from the builder stage
COPY --from=builder /app/search_engine /app/
COPY --from=builder /app/db/schema.sql /app/db/
COPY --from=builder /app/web/static /app/web/static

# Expose the API port
EXPOSE 8080

# Set the entrypoint
ENTRYPOINT ["/app/search_engine"]

# Default command (can be overridden)
CMD ["--api", "--port=8080"]
