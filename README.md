# Search Engine

A Go-based search engine with web crawler, TF-IDF indexing, and a web interface.

## Features

- Web crawler using [Colly](https://github.com/gocolly/colly)
- Text processing with tokenization, stemming, and stop word removal
- TF-IDF based search indexing
- PostgreSQL database for storing webpages and search index
- RESTful API for search functionality
- Simple web UI for searching

## Project Structure

```
search_engine/
├── cmd/                 # Command-line tools
│   └── seed/            # URL seeding tool
├── config/              # Configuration
├── crawler/             # Crawler implementation
├── data/                # Data files
│   └── seed.csv         # Seed URLs for crawler
├── db/                  # Database related code
│   ├── init_db.go       # Database initialization
│   └── schema.sql       # Database schema
├── models/              # Data models
├── pkg/                 # Core packages
│   ├── api/             # API server
│   ├── crawler/         # Web crawler
│   ├── indexer/         # TF-IDF indexing
│   ├── search/          # Search functionality
│   ├── seeder/          # URL seeding functionality
│   └── textprocessor/   # Text processing utilities
├── web/                 # Web interface
│   └── static/          # Static files for web UI
├── main.go              # Main application entry point
└── README.md            # Project documentation
```

## Prerequisites

- Go 1.18 or higher
- PostgreSQL 12 or higher
- Docker (optional, for containerized deployment)

## Installation

1. Clone the repository:

```bash
git clone https://github.com/amankumarsingh77/search_engine.git
cd search_engine
```

2. Install dependencies:

```bash
go mod download
```

3. Set up the database:

```bash
# Start PostgreSQL (if not already running)
# Example with Docker:
docker run --name postgres -e POSTGRES_USER=admin -e POSTGRES_PASSWORD=secret -e POSTGRES_DB=inverted_index_db -p 5433:5432 -d postgres:14

# Initialize the database schema
go run main.go --init-db
```

## Usage

### Running the Crawler

```bash
go run main.go --crawl
```

### Seeding URLs from CSV

You can seed URLs from a CSV file into the Redis queue:

```bash
go run cmd/seed/main.go --file=data/seed.csv --queue=pending
```

The CSV file should have a header row with a column named "url". Each subsequent row should contain a URL to be added to the Redis queue.

Example CSV format:
```csv
url
https://www.example.com
https://www.example.org
https://www.example.net
```

For more details, see the [URL Seeder documentation](cmd/seed/README.md).

### Running the API Server

```bash
go run main.go --api --port=8080
```

#### CORS Configuration

The API server supports CORS configuration through command-line flags:

```bash
# Allow specific origins
go run main.go --api --cors-origins="http://localhost:3000,https://example.com"

# Configure allowed methods
go run main.go --api --cors-methods="GET,POST,PUT,DELETE"

# Configure allowed headers
go run main.go --api --cors-headers="Content-Type,Authorization"

# Set max age for preflight requests (in seconds)
go run main.go --api --cors-max-age=3600

# Allow credentials
go run main.go --api --cors-credentials
```

### Performing a Search from Command Line

```bash
go run main.go --query="bollywood movies"
```

### Using the Web Interface

1. Start the API server:

```bash
go run main.go --api
```

2. Open your browser and navigate to:

```
http://localhost:8080
```

## API Endpoints

- `GET /api/search?q=query` - Search for documents matching the query
  - Optional parameters:
    - `limit` - Maximum number of results to return (default: 20)
    - `offset` - Number of results to skip (default: 0)
- `GET /api/health` - Health check endpoint
- `OPTIONS /*` - CORS preflight requests are handled automatically

All API endpoints support CORS with configurable origins, methods, headers, and credentials.

## Building

To build the executable:

```bash
go build -o search_engine
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- [Colly](https://github.com/gocolly/colly) for web crawling
- [SQLx](https://github.com/jmoiron/sqlx) for database operations
- [Porter Stemmer](https://github.com/reiver/go-porterstemmer) for text stemming
