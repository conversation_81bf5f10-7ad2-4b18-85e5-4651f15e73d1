package seeder

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/redis/go-redis/v9"
)

// CSVSeeder reads URLs from a CSV file and adds them to a Redis queue
type CSVSeeder struct {
	redisClient *redis.Client
	queueName   string
	filePath    string
}

// NewCSVSeeder creates a new CSVSeeder instance
func NewCSVSeeder(redisClient *redis.Client, queueName, filePath string) *CSVSeeder {
	return &CSVSeeder{
		redisClient: redisClient,
		queueName:   queueName,
		filePath:    filePath,
	}
}

// Seed reads URLs from the CSV file and adds them to the Redis queue
func (s *CSVSeeder) Seed(ctx context.Context) (int, error) {
	// Check if file exists
	if _, err := os.Stat(s.filePath); os.IsNotExist(err) {
		return 0, fmt.Errorf("seed file not found: %s", s.filePath)
	}

	// Open the CSV file
	file, err := os.Open(s.filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to open seed file: %w", err)
	}
	defer file.Close()

	// Create a CSV reader
	reader := csv.NewReader(file)
	reader.FieldsPerRecord = -1 // Allow variable number of fields

	// Read and process each row
	count := 0
	batchSize := 1000
	batch := make([]interface{}, 0, batchSize)

	for {
		row, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Printf("Error reading CSV row: %v", err)
			continue
		}

		if len(row) < 2 {
			log.Printf("Row has fewer than 2 columns, skipping: %v", row)
			continue
		}

		// Assume format: id,domain
		domain := strings.TrimSpace(row[1])
		if domain == "" {
			continue
		}

		// Convert domain to full URL
		url := domain
		if !strings.HasPrefix(domain, "http://") && !strings.HasPrefix(domain, "https://") {
			url = "https://" + domain
		}

		batch = append(batch, url)

		// Process batch when it reaches the batch size
		if len(batch) >= batchSize {
			err = s.redisClient.LPush(ctx, s.queueName, batch...).Err()
			if err != nil {
				log.Printf("Error adding batch to Redis queue: %v", err)
			} else {
				count += len(batch)
			}
			batch = batch[:0] // Reset batch
		}
	}

	// Process remaining items in batch
	if len(batch) > 0 {
		err = s.redisClient.LPush(ctx, s.queueName, batch...).Err()
		if err != nil {
			log.Printf("Error adding final batch to Redis queue: %v", err)
		} else {
			count += len(batch)
		}
	}

	return count, nil
}

// ValidateCSVFile checks if the CSV file exists and has the correct format
func ValidateCSVFile(filePath string) error {
	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("seed file not found: %s", filePath)
	}

	// Check file extension
	ext := strings.ToLower(filepath.Ext(filePath))
	if ext != ".csv" {
		return fmt.Errorf("file is not a CSV file: %s", filePath)
	}

	// Open the CSV file
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open seed file: %w", err)
	}
	defer file.Close()

	// Create a CSV reader
	reader := csv.NewReader(file)
	reader.FieldsPerRecord = -1 // Allow variable number of fields

	// Read the first row to validate format
	row, err := reader.Read()
	if err != nil {
		return fmt.Errorf("failed to read CSV first row: %w", err)
	}

	// Check if the row has at least 2 columns (id, domain)
	if len(row) < 2 {
		return fmt.Errorf("CSV file must have at least 2 columns (id, domain)")
	}

	return nil
}
