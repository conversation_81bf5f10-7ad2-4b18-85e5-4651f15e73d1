package seeder

import (
	"context"
	"encoding/csv"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/redis/go-redis/v9"
)

// CSVSeeder reads URLs from a CSV file and adds them to a Redis queue
type CSVSeeder struct {
	redisClient *redis.Client
	queueName   string
	filePath    string
}

// NewCSVSeeder creates a new CSVSeeder instance
func NewCSVSeeder(redisClient *redis.Client, queueName, filePath string) *CSVSeeder {
	return &CSVSeeder{
		redisClient: redisClient,
		queueName:   queueName,
		filePath:    filePath,
	}
}

// Seed reads URLs from the CSV file and adds them to the Redis queue
func (s *CSVSeeder) Seed(ctx context.Context) (int, error) {
	// Check if file exists
	if _, err := os.Stat(s.filePath); os.IsNotExist(err) {
		return 0, fmt.Errorf("seed file not found: %s", s.filePath)
	}

	// Open the CSV file
	file, err := os.Open(s.filePath)
	if err != nil {
		return 0, fmt.Errorf("failed to open seed file: %w", err)
	}
	defer file.Close()

	// Create a CSV reader
	reader := csv.NewReader(file)
	
	// Read the header row
	header, err := reader.Read()
	if err != nil {
		return 0, fmt.Errorf("failed to read CSV header: %w", err)
	}

	// Find the URL column index
	urlColIndex := -1
	for i, col := range header {
		if strings.ToLower(col) == "url" {
			urlColIndex = i
			break
		}
	}

	if urlColIndex == -1 {
		return 0, fmt.Errorf("CSV file does not contain a 'url' column")
	}

	// Read and process each row
	count := 0
	for {
		row, err := reader.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			log.Printf("Error reading CSV row: %v", err)
			continue
		}

		if urlColIndex >= len(row) {
			log.Printf("Row has fewer columns than expected, skipping: %v", row)
			continue
		}

		url := strings.TrimSpace(row[urlColIndex])
		if url == "" {
			continue
		}

		// Add URL to Redis queue
		err = s.redisClient.LPush(ctx, s.queueName, url).Err()
		if err != nil {
			log.Printf("Error adding URL to Redis queue: %v", err)
			continue
		}
		count++
	}

	return count, nil
}

// ValidateCSVFile checks if the CSV file exists and has the correct format
func ValidateCSVFile(filePath string) error {
	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		return fmt.Errorf("seed file not found: %s", filePath)
	}

	// Check file extension
	ext := strings.ToLower(filepath.Ext(filePath))
	if ext != ".csv" {
		return fmt.Errorf("file is not a CSV file: %s", filePath)
	}

	// Open the CSV file
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open seed file: %w", err)
	}
	defer file.Close()

	// Create a CSV reader
	reader := csv.NewReader(file)
	
	// Read the header row
	header, err := reader.Read()
	if err != nil {
		return fmt.Errorf("failed to read CSV header: %w", err)
	}

	// Check if the header contains a URL column
	hasURLColumn := false
	for _, col := range header {
		if strings.ToLower(col) == "url" {
			hasURLColumn = true
			break
		}
	}

	if !hasURLColumn {
		return fmt.Errorf("CSV file does not contain a 'url' column")
	}

	return nil
}
