package crawler

import (
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/amankumarsingh77/search_engine/pkg/indexer"
	"github.com/amankumarsingh77/search_engine/pkg/textprocessor"
	"github.com/gocolly/colly"
	"github.com/jmoiron/sqlx"
)

// WebPage represents the crawled data from a webpage
type WebPage struct {
	DocID       int64    `json:"doc_id,omitempty"`
	URL         string   `json:"url"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Keywords    []string `json:"keywords"`

	Headings      map[string][]string `json:"headings"`
	Paragraphs    []string            `json:"paragraphs"`
	BodyText      string              `json:"body_text"`
	InternalLinks []string            `json:"internal_links"`
	ExternalLinks []string            `json:"external_links"`
}

// CrawlerConfig contains configuration for the crawler
type CrawlerConfig struct {
	UserAgent      string
	MaxDepth       int
	Parallelism    int
	Delay          time.Duration
	AllowedDomains []string
	IgnoreRobots   bool
}

// DefaultCrawlerConfig returns default crawler configuration
func DefaultCrawlerConfig() CrawlerConfig {
	return CrawlerConfig{
		UserAgent:      "GoSearchEngineCrawler/1.0 (+http://example.com/bot)",
		MaxDepth:       2,
		Parallelism:    5,
		Delay:          1 * time.Second,
		AllowedDomains: []string{},
		IgnoreRobots:   false,
	}
}

// SaveWebpageAndGetID saves webpage info to DB and returns its doc_id
func SaveWebpageAndGetID(db *sqlx.DB, page WebPage) (int64, error) {
	keywords := strings.Join(page.Keywords, ",")
	var docID int64

	// Try to get existing doc_id for the URL first to handle potential race conditions
	err := db.Get(&docID, "SELECT doc_id FROM webpages WHERE url = $1", page.URL)
	if err == nil {
		// URL exists, update its content
		updateSQL := `
            UPDATE webpages SET title = $1, description = $2, keywords = $3, updated_at = CURRENT_TIMESTAMP
            WHERE doc_id = $4`
		_, err = db.Exec(updateSQL, page.Title, page.Description, keywords, docID)
		if err != nil {
			return 0, fmt.Errorf("failed to update webpage (doc_id: %d): %w", docID, err)
		}
		return docID, nil
	} else if err.Error() != "sql: no rows in result set" {
		return 0, fmt.Errorf("failed to check existing webpage by URL %s: %w", page.URL, err)
	}

	// URL does not exist, insert new record
	insertSQL := `
        INSERT INTO webpages (url, title, description, keywords)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (url) DO UPDATE SET
            title = EXCLUDED.title,
            description = EXCLUDED.description,
            keywords = EXCLUDED.keywords,
            updated_at = CURRENT_TIMESTAMP
        RETURNING doc_id
    `
	err = db.QueryRowx(insertSQL, page.URL, page.Title, page.Description, keywords).Scan(&docID)
	if err != nil {
		return 0, fmt.Errorf("failed to insert/update webpage %s: %w", page.URL, err)
	}
	return docID, nil
}

// Crawl crawls the given URLs and indexes them
func Crawl(db *sqlx.DB, startURLs []string, config CrawlerConfig) error {
	c := colly.NewCollector(
		colly.Async(true),
		colly.UserAgent(config.UserAgent),
		colly.IgnoreRobotsTxt(),
		colly.MaxDepth(config.MaxDepth),
	)

	// if len(config.AllowedDomains) > 0 {
	// 	c.AllowedDomains(config.AllowedDomains...)
	// }

	err := c.Limit(&colly.LimitRule{
		DomainGlob:  "*",
		Parallelism: config.Parallelism,
		Delay:       config.Delay,
	})
	if err != nil {
		return fmt.Errorf("failed to set limit rule: %w", err)
	}

	c.OnHTML("html", func(e *colly.HTMLElement) {
		doc := e.DOM
		pageURL := e.Request.URL.String()

		// Extract content
		title := strings.TrimSpace(doc.Find("title").Text())
		description := strings.TrimSpace(doc.Find("meta[name='description']").AttrOr("content", ""))
		keywordsRaw := strings.TrimSpace(doc.Find("meta[name='keywords']").AttrOr("content", ""))
		var keywords []string
		if keywordsRaw != "" {
			keywords = strings.Split(keywordsRaw, ",")
			for i, k := range keywords {
				keywords[i] = strings.TrimSpace(k)
			}
		}

		var bodyTextBuilder strings.Builder
		doc.Find("h1, h2, h3, h4, h5, h6, p, span, div").Each(func(_ int, s *goquery.Selection) {
			// Avoid script/style content if they are inside these tags
			if s.Is("script") || s.Is("style") {
				return
			}
			bodyTextBuilder.WriteString(strings.TrimSpace(s.Text()))
			bodyTextBuilder.WriteString("\n")
		})

		pageData := WebPage{
			URL:         pageURL,
			Title:       title,
			Description: description,
			Keywords:    keywords,
			BodyText:    bodyTextBuilder.String(),
		}

		// Save webpage to DB and get its doc_id
		docID, err := SaveWebpageAndGetID(db, pageData)
		if err != nil {
			log.Printf("Error saving webpage %s: %v", pageURL, err)
			return // Skip indexing if page saving fails
		}

		// Preprocess and add to in-memory TF/DocLength stores
		tokens := textprocessor.Preprocess(pageData.Title + " " + pageData.Description + " " + pageData.BodyText)
		if len(tokens) > 0 {
			indexer.AddToIndexAndDocLength(indexer.GlobalRawTFData, indexer.GlobalDocLengths, tokens, docID)
		}

		// Find and visit more links
		doc.Find("a[href]").Each(func(_ int, s *goquery.Selection) {
			link := e.Request.AbsoluteURL(s.AttrOr("href", ""))
			if link != "" && config.MaxDepth > 1 {
				e.Request.Visit(link)
			}
		})
	})

	c.OnRequest(func(r *colly.Request) {
		log.Println("Visiting", r.URL.String())
	})

	c.OnError(func(r *colly.Response, err error) {
		log.Printf("Error crawling %s: %v (Status: %d)", r.Request.URL.String(), err, r.StatusCode)
	})

	c.OnScraped(func(r *colly.Response) {
		log.Println("Finished scraping", r.Request.URL.String())
	})

	for _, url := range startURLs {
		err := c.Visit(url)
		if err != nil {
			log.Printf("Error initiating visit to %s: %v", url, err)
		}
	}

	log.Println("Crawler started. Waiting for tasks to complete...")
	c.Wait() // Wait for all crawling tasks to finish
	log.Println("Crawling finished.")

	return nil
}
