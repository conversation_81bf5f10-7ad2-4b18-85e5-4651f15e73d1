package search

import (
	"fmt"
	"log"

	"github.com/aman<PERSON>singh77/search_engine/pkg/textprocessor"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
)

// SearchResult represents a search result item
type SearchResult struct {
	DocID       int64   `db:"doc_id" json:"doc_id"`
	URL         string  `db:"url" json:"url"`
	Title       string  `db:"title" json:"title"`
	Description string  `db:"description" json:"description"`
	Keywords    string  `db:"keywords" json:"keywords"`
	Score       float64 `db:"score" json:"score"`
}

// SearchOptions contains options for the search
type SearchOptions struct {
	Limit  int
	Offset int
}

// DefaultSearchOptions returns default search options
func DefaultSearchOptions() SearchOptions {
	return SearchOptions{
		Limit:  20,
		Offset: 0,
	}
}

// Search performs a search based on TF-IDF scores
func Search(db *sqlx.DB, query string, options SearchOptions) ([]SearchResult, error) {
	processedQueryTokens := textprocessor.Preprocess(query)
	if len(processedQueryTokens) == 0 {
		return nil, fmt.Errorf("no valid tokens in query after preprocessing")
	}

	// Get token_ids for the processed query tokens
	var queryTokenIDs []int
	for _, tokenText := range processedQueryTokens {
		var tokenID int
		err := db.Get(&tokenID, "SELECT id FROM tokens WHERE token_text = $1", tokenText)
		if err != nil {
			if err.Error() == "sql: no rows in result set" {
				// Token not in our vocabulary, skip it for search
				log.Printf("Query token '%s' not found in vocabulary.", tokenText)
				continue
			} else {
				return nil, fmt.Errorf("error fetching token_id for '%s': %w", tokenText, err)
			}
		}
		queryTokenIDs = append(queryTokenIDs, tokenID)
	}

	if len(queryTokenIDs) == 0 {
		return nil, fmt.Errorf("no query tokens found in vocabulary")
	}

	// SQL to retrieve relevant documents and sum their TF-IDF scores for matching tokens
	sqlQuery := `
        SELECT
            td.doc_id,
            w.url,
            w.title,
            w.description,
            w.keywords,
            SUM(td.tfidf_score) as score
        FROM token_documents td
        JOIN webpages w ON td.doc_id = w.doc_id
        WHERE td.token_id = ANY($1)
        GROUP BY td.doc_id, w.url, w.title, w.description, w.keywords
        ORDER BY score DESC
        LIMIT $2
        OFFSET $3
    `

	var results []SearchResult
	err := db.Select(&results, sqlQuery, pq.Array(queryTokenIDs), options.Limit, options.Offset)
	if err != nil {
		return nil, fmt.Errorf("search query failed: %w", err)
	}
	return results, nil
}
