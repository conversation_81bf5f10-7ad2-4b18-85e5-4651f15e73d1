package api

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/amankumarsingh77/search_engine/pkg/search"
	"github.com/jmoiron/sqlx"
)

// Server represents the API server
type Server struct {
	DB         *sqlx.DB
	Port       int
	Router     *http.ServeMux
	CORSConfig CORSConfig
}

// NewServer creates a new API server

// CORSConfig holds configuration for CORS middleware
type CORSConfig struct {
	AllowedOrigins   []string
	AllowedMethods   []string
	AllowedHeaders   []string
	AllowCredentials bool
	MaxAge           int
}

// DefaultCORSConfig returns a default CORS configuration
func DefaultCORSConfig() CORSConfig {
	return CORSConfig{
		AllowedOrigins:   []string{"*"},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"},
		AllowedHeaders:   []string{"Content-Type", "Authorization", "Accept", "X-Requested-With"},
		AllowCredentials: false,
		MaxAge:           86400, // 24 hours
	}
}

// corsMiddleware creates a middleware that handles CORS
func (s *Server) corsMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Set CORS headers
		origin := r.Header.Get("Origin")

		// Check if the origin is allowed
		allowOrigin := "*"
		if origin != "" && s.CORSConfig.AllowedOrigins[0] != "*" {
			allowOrigin = ""
			for _, allowedOrigin := range s.CORSConfig.AllowedOrigins {
				if allowedOrigin == origin {
					allowOrigin = origin
					break
				}
			}
			if allowOrigin == "" {
				// Origin not allowed
				next.ServeHTTP(w, r)
				return
			}
		}

		w.Header().Set("Access-Control-Allow-Origin", allowOrigin)

		// Set other CORS headers
		w.Header().Set("Access-Control-Allow-Methods", strings.Join(s.CORSConfig.AllowedMethods, ", "))
		w.Header().Set("Access-Control-Allow-Headers", strings.Join(s.CORSConfig.AllowedHeaders, ", "))

		if s.CORSConfig.AllowCredentials {
			w.Header().Set("Access-Control-Allow-Credentials", "true")
		}

		if s.CORSConfig.MaxAge > 0 {
			w.Header().Set("Access-Control-Max-Age", strconv.Itoa(s.CORSConfig.MaxAge))
		}

		// Handle preflight requests
		if r.Method == http.MethodOptions {
			w.WriteHeader(http.StatusOK)
			return
		}

		// Continue to the actual handler
		next.ServeHTTP(w, r)
	})
}

// NewServer creates a new API server with default configuration
func NewServer(db *sqlx.DB, port int) *Server {
	return NewServerWithConfig(db, port, DefaultCORSConfig())
}

// NewServerWithConfig creates a new API server with custom configuration
func NewServerWithConfig(db *sqlx.DB, port int, corsConfig CORSConfig) *Server {
	server := &Server{
		DB:         db,
		Port:       port,
		Router:     http.NewServeMux(),
		CORSConfig: corsConfig,
	}
	server.setupRoutes()
	return server
}

// setupRoutes sets up the API routes
func (s *Server) setupRoutes() {
	s.Router.HandleFunc("/api/search", s.handleSearch)
	s.Router.HandleFunc("/api/health", s.handleHealth)

	// Serve static files for the web UI
	fs := http.FileServer(http.Dir("./web/static"))
	s.Router.Handle("/", fs)
}

// handleSearch handles search requests
func (s *Server) handleSearch(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	query := r.URL.Query().Get("q")
	if query == "" {
		http.Error(w, "Query parameter 'q' is required", http.StatusBadRequest)
		return
	}

	limitStr := r.URL.Query().Get("limit")
	offsetStr := r.URL.Query().Get("offset")

	options := search.DefaultSearchOptions()

	if limitStr != "" {
		limit, err := strconv.Atoi(limitStr)
		if err == nil && limit > 0 {
			options.Limit = limit
		}
	}

	if offsetStr != "" {
		offset, err := strconv.Atoi(offsetStr)
		if err == nil && offset >= 0 {
			options.Offset = offset
		}
	}

	startTime := time.Now()
	results, err := search.Search(s.DB, query, options)
	if err != nil {
		log.Printf("Search error: %v", err)
		http.Error(w, "Search failed", http.StatusInternalServerError)
		return
	}
	duration := time.Since(startTime)

	response := struct {
		Query    string                `json:"query"`
		Results  []search.SearchResult `json:"results"`
		Count    int                   `json:"count"`
		Duration string                `json:"duration"`
	}{
		Query:    query,
		Results:  results,
		Count:    len(results),
		Duration: duration.String(),
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Error encoding response", http.StatusInternalServerError)
	}
}

// handleHealth handles health check requests
func (s *Server) handleHealth(w http.ResponseWriter, r *http.Request) {
	err := s.DB.Ping()
	if err != nil {
		log.Printf("Health check failed: %v", err)
		http.Error(w, "Database connection failed", http.StatusInternalServerError)
		return
	}

	response := struct {
		Status  string `json:"status"`
		Message string `json:"message"`
	}{
		Status:  "ok",
		Message: "Service is healthy",
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Error encoding health response: %v", err)
		http.Error(w, "Error encoding response", http.StatusInternalServerError)
	}
}

// Start starts the API server
func (s *Server) Start() error {
	addr := fmt.Sprintf(":%d", s.Port)
	log.Printf("Starting API server on %s", addr)

	// Wrap the router with the CORS middleware
	handler := s.corsMiddleware(s.Router)

	return http.ListenAndServe(addr, handler)
}
