package textprocessor

import (
	"log"
	"regexp"
	"strings"

	"github.com/reiver/go-porterstemmer"
	"golang.org/x/text/unicode/norm"
)

// StopWords is a map of common English stop words
var StopWords = map[string]bool{
	"a": true, "an": true, "the": true, "and": true, "or": true, "but": true, "is": true, "are": true,
	"in": true, "on": true, "it": true, "this": true, "that": true, "to": true, "for": true, "of": true,
	"with": true, "as": true, "at": true, "by": true, "from": true, "has": true, "have": true, "had": true,
	"be": true, "been": true, "being": true, "was": true, "were": true, "will": true, "would": true,
	"could": true, "should": true, "can": true, "may": true, "might": true, "must": true, "shall": true,
	"i": true, "you": true, "he": true, "she": true, "they": true, "we": true, "me": true, "him": true,
	"her": true, "them": true, "us": true, "my": true, "your": true, "his": true, "their": true, "our": true,
	"do": true, "does": true, "did": true, "done": true, "am": true, "if": true, "so": true, "no": true,
	"not": true, "yes": true, "up": true, "down": true, "out": true, "over": true, "under": true,
}

// Normalize converts text to lowercase, removes non-alphabetic characters, and normalizes whitespace
func Normalize(text string) string {
	text = strings.ToLower(text)

	// Remove citation-like patterns [12], [citation needed]
	citation := regexp.MustCompile(`\[\d+[a-zA-Z]*\]`)
	text = citation.ReplaceAllString(text, "")

	// Remove markdown links [text](url)
	markdownLink := regexp.MustCompile(`\[(.*?)\]\((.*?)\)`)
	text = markdownLink.ReplaceAllString(text, "$1")

	// Remove all non-alphabetic characters (including numbers)
	nonAlpha := regexp.MustCompile(`[^a-z\s]`)
	text = nonAlpha.ReplaceAllString(text, " ")

	// Replace multiple spaces with single space
	space := regexp.MustCompile(`\s+`)
	text = space.ReplaceAllString(text, " ")

	return norm.NFC.String(strings.TrimSpace(text))
}

// TokenizeAndFilter splits text into tokens and removes stop words
func TokenizeAndFilter(text string) []string {
	tokens := strings.Fields(text)
	var filtered []string
	for _, token := range tokens {
		if !StopWords[token] && len(token) > 1 { // Filter out stop words and very short tokens
			filtered = append(filtered, token)
		}
	}
	return filtered
}

// StemTokens applies Porter stemming to tokens
func StemTokens(tokens []string) []string {
	var res []string
	for _, token := range tokens {
		// Each token here is already > 1 character due to tokenizeAndFilter
		func() {
			defer func() {
				if r := recover(); r != nil {
					// Log the error and the token that caused the panic
					log.Printf("WARNING: Recovered from panic while stemming token '%s': %v", token, r)
					// Add the original, unstemmed token
					res = append(res, token)
				}
			}()
			stemmed := porterstemmer.StemString(token)
			res = append(res, stemmed)
		}()
	}
	return res
}

// Preprocess applies the full text processing pipeline: normalize, tokenize, filter, and stem
func Preprocess(text string) []string {
	text = Normalize(text)
	tokens := TokenizeAndFilter(text)
	tokens = StemTokens(tokens)
	return tokens
}
