package indexer

import (
	"fmt"
	"log"
	"math"
	"sync"

	"github.com/jmoiron/sqlx"
)

// RawTFData stores raw term frequencies: map[token_text]map[doc_id]raw_tf_count
type RawTFData map[string]map[int64]int

// DocLengths stores the total number of terms for each document: map[doc_id]length
type DocLengths map[int64]int

// IDFStore stores IDF for each token_id: map[token_id]float64
type IDFStore map[int]float64

// TFIDFScore represents a TF-IDF score for a token in a document
type TFIDFScore struct {
	TokenID    int
	DocID      int64
	TFIDFScore float64
}

// Global data structures for collecting TF and doc lengths during crawl
var (
	GlobalRawTFData  = make(RawTFData)
	GlobalDocLengths = make(DocLengths)
	GlobalDataMutex  = &sync.Mutex{} // To protect shared GlobalRawTFData and GlobalDocLengths
)

// AddToIndexAndDocLength populates the raw TF index and tracks document lengths
func AddToIndexAndDocLength(rawTFs RawTFData, docLengths DocLengths, tokens []string, docID int64) {
	GlobalDataMutex.Lock() // Protect shared maps
	defer GlobalDataMutex.Unlock()

	if _, exists := docLengths[docID]; !exists {
		docLengths[docID] = 0
	}
	docLengths[docID] += len(tokens)

	for _, token := range tokens {
		if _, exists := rawTFs[token]; !exists {
			rawTFs[token] = make(map[int64]int)
		}
		rawTFs[token][docID]++
	}
}

// CalculateIDF calculates the Inverse Document Frequency for each token_id
func CalculateIDF(tokenIDToDocFreq map[int]int, totalDocs int) IDFStore {
	idfStore := make(IDFStore)
	if totalDocs == 0 {
		return idfStore
	}
	for tokenID, numDocsContainingToken := range tokenIDToDocFreq {
		if numDocsContainingToken > 0 {
			// Using a common smoothed IDF formula: log(1 + (N - df + 0.5) / (df + 0.5))
			// This variant is often used in systems like Lucene (BM25 IDF component)
			// and helps ensure positive values and handles terms appearing in many documents.
			idfScore := math.Log(1 + (float64(totalDocs-numDocsContainingToken)+0.5)/(float64(numDocsContainingToken)+0.5))
			idfStore[tokenID] = idfScore
		}
	}
	return idfStore
}

// GetOrInsertTokenID retrieves a token's ID from the DB, inserting it if it doesn't exist
func GetOrInsertTokenID(db *sqlx.Tx, tokenText string) (int, error) {
	var tokenID int
	err := db.Get(&tokenID, "SELECT id FROM tokens WHERE token_text = $1", tokenText)
	if err != nil {
		if err.Error() == "sql: no rows in result set" {
			err = db.QueryRowx("INSERT INTO tokens (token_text) VALUES ($1) RETURNING id", tokenText).Scan(&tokenID)
			if err != nil {
				return 0, fmt.Errorf("failed to insert token '%s': %w", tokenText, err)
			}
		} else {
			return 0, fmt.Errorf("failed to query token '%s': %w", tokenText, err)
		}
	}
	return tokenID, nil
}

// ProcessAndStoreTFIDF calculates and stores TF-IDF scores in the database
func ProcessAndStoreTFIDF(db *sqlx.DB, rawTFs RawTFData, docLengths DocLengths) error {
	log.Println("Starting TF-IDF calculation and storage...")
	totalNumberOfDocuments := len(docLengths)
	if totalNumberOfDocuments == 0 {
		log.Println("No documents processed, skipping TF-IDF calculation.")
		return nil
	}

	tokenTextToIDMap := make(map[string]int)
	tokenIDToDocFreq := make(map[int]int) // Stores document frequency for each token_id

	log.Println("Step 1: Populating tokens table and collecting document frequencies...")
	// First pass: ensure all tokens are in DB and get their IDs, and count document frequencies
	tx, err := db.Beginx()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for token processing: %w", err)
	}
	defer tx.Rollback() // Rollback if not committed

	for tokenText, docMap := range rawTFs {
		tokenID, err := GetOrInsertTokenID(tx, tokenText) // Use transaction for token insertion
		if err != nil {
			return fmt.Errorf("error getting/inserting token_id for '%s': %w", tokenText, err)
		}
		tokenTextToIDMap[tokenText] = tokenID
		tokenIDToDocFreq[tokenID] = len(docMap)
	}
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction for token processing: %w", err)
	}
	log.Printf("Processed %d unique tokens.", len(tokenTextToIDMap))

	log.Println("Step 2: Calculating IDF scores...")
	idfScores := CalculateIDF(tokenIDToDocFreq, totalNumberOfDocuments)

	log.Println("Step 3: Calculating TF-IDF and saving to token_documents...")
	insertTFIDFSQL := `
        INSERT INTO token_documents (token_id, doc_id, tfidf_score)
        VALUES ($1, $2, $3)
        ON CONFLICT (token_id, doc_id) DO UPDATE SET tfidf_score = EXCLUDED.tfidf_score
    `
	// Begin a new transaction for batch inserting TF-IDF scores
	tfidfTx, err := db.Beginx()
	if err != nil {
		return fmt.Errorf("failed to begin transaction for TF-IDF storage: %w", err)
	}
	defer tfidfTx.Rollback()

	stmt, err := tfidfTx.Preparex(insertTFIDFSQL)
	if err != nil {
		return fmt.Errorf("failed to prepare TF-IDF insert statement: %w", err)
	}
	defer stmt.Close()

	count := 0
	for tokenText, docMap := range rawTFs {
		tokenID, ok := tokenTextToIDMap[tokenText]
		if !ok {
			log.Printf("Warning: Token text '%s' not found in tokenTextToIDMap. Skipping.", tokenText)
			continue
		}

		idf := idfScores[tokenID]

		for docID, rawTFCount := range docMap {
			docLen := docLengths[docID]
			var normalizedTF float64
			if docLen > 0 {
				normalizedTF = float64(rawTFCount) / float64(docLen)
			}

			tfidfScore := normalizedTF * idf
			if tfidfScore > 0 { // Only store if TF-IDF is meaningful
				_, err := stmt.Exec(tokenID, docID, tfidfScore)
				if err != nil {
					return fmt.Errorf("failed to insert/update TF-IDF for token_id %d, doc_id %d: %w", tokenID, docID, err)
				}
				count++
				if count%1000 == 0 { // Log progress
					log.Printf("Inserted/Updated %d TF-IDF scores...", count)
				}
			}
		}
	}

	if err := tfidfTx.Commit(); err != nil {
		return fmt.Errorf("failed to commit TF-IDF scores: %w", err)
	}

	log.Printf("Successfully calculated and stored %d TF-IDF scores.", count)
	return nil
}
