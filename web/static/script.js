document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.getElementById('search-form');
    const searchInput = document.getElementById('search-input');
    const resultsInfo = document.getElementById('results-info');
    const resultsList = document.getElementById('results-list');

    // Check for query parameter in URL
    const urlParams = new URLSearchParams(window.location.search);
    const queryParam = urlParams.get('q');
    
    if (queryParam) {
        searchInput.value = queryParam;
        performSearch(queryParam);
    }

    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const query = searchInput.value.trim();
        
        if (query) {
            // Update URL with search query
            const url = new URL(window.location);
            url.searchParams.set('q', query);
            window.history.pushState({}, '', url);
            
            performSearch(query);
        }
    });

    function performSearch(query) {
        resultsInfo.textContent = 'Searching...';
        resultsList.innerHTML = '';

        fetch(`http://localhost:8080/api/search?q=${encodeURIComponent(query)}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Search request failed');
                }
                return response.json();
            })
            .then(data => {
                displayResults(data, query);
            })
            .catch(error => {
                console.error('Error:', error);
                resultsInfo.textContent = 'An error occurred while searching. Please try again.';
            });
    }

    function displayResults(data, query) {
        if (data.count === 0) {
            resultsInfo.textContent = `No results found for "${query}"`;
            resultsList.innerHTML = '<div class="no-results">No matching documents found. Try a different search term.</div>';
            return;
        }

        resultsInfo.textContent = `Found ${data.count} results for "${query}" (${data.duration})`;
        
        resultsList.innerHTML = '';
        data.results.forEach(result => {
            const resultItem = document.createElement('div');
            resultItem.className = 'result-item';
            
            const title = result.title || 'Untitled';
            const description = result.description || 'No description available';
            
            resultItem.innerHTML = `
                <div class="result-title"><a href="${result.url}" target="_blank">${title}</a></div>
                <div class="result-url">${result.url}</div>
                <div class="result-description">${description}</div>
                <div class="result-score">Score: ${result.score.toFixed(6)}</div>
            `;
            
            resultsList.appendChild(resultItem);
        });
    }
});
