* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    font-size: 2.5rem;
    color: #2c3e50;
}

.search-container {
    margin-bottom: 30px;
}

#search-form {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
}

#search-input {
    flex: 1;
    padding: 12px 15px;
    font-size: 1rem;
    border: 2px solid #ddd;
    border-radius: 4px 0 0 4px;
    outline: none;
}

#search-input:focus {
    border-color: #3498db;
}

button {
    padding: 12px 20px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

button:hover {
    background-color: #2980b9;
}

#results-container {
    max-width: 800px;
    margin: 0 auto;
}

#results-info {
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    font-size: 0.9rem;
    color: #666;
}

.result-item {
    margin-bottom: 20px;
    padding: 15px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.result-title {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.result-title a {
    color: #2c3e50;
    text-decoration: none;
}

.result-title a:hover {
    text-decoration: underline;
}

.result-url {
    color: #27ae60;
    font-size: 0.9rem;
    margin-bottom: 10px;
    word-break: break-all;
}

.result-description {
    color: #555;
    margin-bottom: 10px;
}

.result-score {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.no-results {
    text-align: center;
    padding: 20px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
