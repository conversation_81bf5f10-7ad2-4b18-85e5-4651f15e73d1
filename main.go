package main

import (
	"flag"
	"fmt"
	"log"
	"strings"
	"time"

	"github.com/amankumarsingh77/search_engine/db"
	"github.com/amankumarsingh77/search_engine/pkg/api"
	"github.com/amankumarsingh77/search_engine/pkg/crawler"
	"github.com/amankumarsingh77/search_engine/pkg/indexer"
	"github.com/amankumarsingh77/search_engine/pkg/search"
)

func main() {
	// Define command-line flags
	var (
		initDB           bool
		crawl            bool
		apiServer        bool
		port             int
		query            string
		corsAllowOrigins string
		corsAllowHeaders string
		corsAllowMethods string
		corsMaxAge       int
		corsCredentials  bool
	)

	flag.BoolVar(&initDB, "init-db", false, "Initialize the database schema")
	flag.BoolVar(&crawl, "crawl", false, "Run the crawler")
	flag.BoolVar(&apiServer, "api", false, "Start the API server")
	flag.IntVar(&port, "port", 8080, "Port for the API server")
	flag.StringVar(&query, "query", "", "Search query")

	// CORS configuration flags
	flag.StringVar(&corsAllowOrigins, "cors-origins", "*", "Comma-separated list of allowed origins")
	flag.StringVar(&corsAllowHeaders, "cors-headers", "Content-Type,Authorization,Accept,X-Requested-With", "Comma-separated list of allowed headers")
	flag.StringVar(&corsAllowMethods, "cors-methods", "GET,POST,PUT,DELETE,OPTIONS,PATCH", "Comma-separated list of allowed methods")
	flag.IntVar(&corsMaxAge, "cors-max-age", 86400, "Max age for CORS preflight requests in seconds")
	flag.BoolVar(&corsCredentials, "cors-credentials", false, "Allow credentials")

	flag.Parse()

	// Get database connection
	database, err := db.GetDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer database.Close()

	// Initialize database if requested
	if initDB {
		log.Println("Initializing database schema...")
		if err := db.InitDatabase(db.GetDSN()); err != nil {
			log.Fatalf("Failed to initialize database: %v", err)
		}
		log.Println("Database schema initialized successfully.")
		return
	}

	// Run crawler if requested
	if crawl {
		log.Println("Starting crawler...")

		// Define URLs to crawl
		startURLs := []string{
			"https://www.imdb.com/india/top-rated-indian-movies/",
			"https://www.bollywoodhungama.com/movie-release-dates/",
			"https://www.filmfare.com/",
			"https://www.pinkvilla.com/entertainment/bollywood",
			"https://www.koimoi.com/",
			"https://www.bollywoodlife.com/",
			"https://timesofindia.indiatimes.com/entertainment/hindi/bollywood/news",
			// Add more URLs as needed
		}

		// Configure crawler
		config := crawler.DefaultCrawlerConfig()
		config.MaxDepth = 2
		config.Parallelism = 5

		// Start crawling
		if err := crawler.Crawl(database, startURLs, config); err != nil {
			log.Fatalf("Crawler error: %v", err)
		}

		// Process TF-IDF after crawling
		if err := indexer.ProcessAndStoreTFIDF(database, indexer.GlobalRawTFData, indexer.GlobalDocLengths); err != nil {
			log.Fatalf("Failed to process and store TF-IDF: %v", err)
		}

		log.Println("Crawling and indexing completed successfully.")
		return
	}

	// Perform search if query is provided
	if query != "" {
		log.Printf("Searching for: '%s'", query)
		startTime := time.Now()

		searchResults, err := search.Search(database, query, search.DefaultSearchOptions())
		if err != nil {
			log.Fatalf("Search error: %v", err)
		}

		searchDuration := time.Since(startTime)

		fmt.Println("\n--- Search Results ---")
		if len(searchResults) == 0 {
			fmt.Println("No results found.")
		}

		for i, res := range searchResults {
			fmt.Printf("%d. URL: %s\n", i+1, res.URL)
			fmt.Printf("   Title: %s\n", res.Title)
			fmt.Printf("   Score: %f\n", res.Score)
			fmt.Println("---")
		}

		fmt.Printf("Search took %s\n", searchDuration)
		return
	}

	// Start API server if requested
	if apiServer {
		log.Printf("Starting API server on port %d...", port)

		// Parse CORS configuration
		corsConfig := api.DefaultCORSConfig()

		if corsAllowOrigins != "" {
			corsConfig.AllowedOrigins = strings.Split(corsAllowOrigins, ",")
		}

		if corsAllowHeaders != "" {
			corsConfig.AllowedHeaders = strings.Split(corsAllowHeaders, ",")
		}

		if corsAllowMethods != "" {
			corsConfig.AllowedMethods = strings.Split(corsAllowMethods, ",")
		}

		corsConfig.MaxAge = corsMaxAge
		corsConfig.AllowCredentials = corsCredentials

		// Create server with custom CORS configuration
		server := api.NewServerWithConfig(database, port, corsConfig)

		log.Printf("CORS configuration: Origins=%v, Methods=%v, Headers=%v, MaxAge=%d, Credentials=%v",
			corsConfig.AllowedOrigins, corsConfig.AllowedMethods, corsConfig.AllowedHeaders,
			corsConfig.MaxAge, corsConfig.AllowCredentials)

		if err := server.Start(); err != nil {
			log.Fatalf("API server error: %v", err)
		}
		return
	}

	// If no specific action is requested, print usage
	if !initDB && !crawl && !apiServer && query == "" {
		fmt.Println("Usage:")
		fmt.Println("  --init-db                Initialize the database schema")
		fmt.Println("  --crawl                  Run the crawler")
		fmt.Println("  --api                    Start the API server")
		fmt.Println("  --port=PORT              Port for the API server (default: 8080)")
		fmt.Println("  --query=QUERY            Perform a search query")
		fmt.Println("\nCORS Configuration (for API server):")
		fmt.Println("  --cors-origins=ORIGINS   Comma-separated list of allowed origins (default: *)")
		fmt.Println("  --cors-headers=HEADERS   Comma-separated list of allowed headers")
		fmt.Println("  --cors-methods=METHODS   Comma-separated list of allowed methods")
		fmt.Println("  --cors-max-age=SECONDS   Max age for CORS preflight requests (default: 86400)")
		fmt.Println("  --cors-credentials       Allow credentials (default: false)")
		fmt.Println("\nExamples:")
		fmt.Println("  ./search_engine --init-db")
		fmt.Println("  ./search_engine --crawl")
		fmt.Println("  ./search_engine --api --port=8080")
		fmt.Println("  ./search_engine --api --cors-origins=\"http://localhost:3000,https://example.com\"")
		fmt.Println("  ./search_engine --query=\"bollywood movies\"")
	}
}
