package main

import (
	"context"
	"flag"
	"log"
	"os"
	"time"

	"github.com/amankumarsingh77/search_engine/config"
	"github.com/amankumarsingh77/search_engine/crawler"
	"github.com/amankumarsingh77/search_engine/pkg/seeder"
)

func main() {
	// Define command-line flags
	var (
		seedFile  string
		queueName string
	)

	flag.StringVar(&seedFile, "file", "data/seed.csv", "Path to the seed CSV file")
	flag.StringVar(&queueName, "queue", "pending", "Name of the Redis queue to seed")
	flag.Parse()

	// Load configuration
	cfg, err := config.LoadCrawlerConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Validate the seed file
	if err := seeder.ValidateCSVFile(seedFile); err != nil {
		log.Fatalf("Invalid seed file: %v", err)
	}

	// Create Redis client
	ctx := context.Background()
	redisClient, err := crawler.NewRedisClient(ctx, &cfg.Redis)
	if err != nil {
		log.Fatalf("Failed to connect to Redis: %v", err)
	}
	defer redisClient.Close()

	// Create seeder
	csvSeeder := seeder.NewCSVSeeder(redisClient, queueName, seedFile)

	// Start seeding
	log.Printf("Starting to seed URLs from %s to Redis queue '%s'...", seedFile, queueName)
	startTime := time.Now()

	count, err := csvSeeder.Seed(ctx)
	if err != nil {
		log.Fatalf("Seeding error: %v", err)
	}

	duration := time.Since(startTime)
	log.Printf("Successfully seeded %d URLs in %s", count, duration)

	// Get queue size
	queueSize, err := redisClient.LLen(ctx, queueName).Result()
	if err != nil {
		log.Printf("Warning: Failed to get queue size: %v", err)
	} else {
		log.Printf("Current queue size: %d", queueSize)
	}

	os.Exit(0)
}
