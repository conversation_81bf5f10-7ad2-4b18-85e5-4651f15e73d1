# URL Seeder for Search Engine

This tool seeds URLs from a CSV file into the Redis queue for the search engine crawler.

## Usage

```bash
go run cmd/seed/main.go [options]
```

### Options

- `--file`: Path to the seed CSV file (default: "data/seed.csv")
- `--queue`: Name of the Redis queue to seed (default: "pending")

### Example

```bash
# Use the default seed file and queue
go run cmd/seed/main.go

# Specify a custom seed file
go run cmd/seed/main.go --file=custom_seeds.csv

# Specify a custom queue name
go run cmd/seed/main.go --queue=custom_queue
```

## CSV File Format

The CSV file should have a header row with a column named "url". Each subsequent row should contain a URL to be added to the Redis queue.

Example:

```csv
url
https://www.example.com
https://www.example.org
https://www.example.net
```

## Building

To build the seeder as a standalone executable:

```bash
go build -o seed_urls cmd/seed/main.go
```

Then you can run it:

```bash
./seed_urls --file=data/seed.csv
```
