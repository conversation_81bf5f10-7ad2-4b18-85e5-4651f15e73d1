# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib
search_engine

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# OS specific files
.DS_Store
Thumbs.db

# Log files
*.log

# Temporary files
*.tmp
*.temp

# JSON files (often used for test data)
*.json

# Compressed files
*.gz
*.zip
*.tar
*.rar
pages.ndjson.gz

# Database files
*.db
*.sqlite

# Environment variables
.env
.env.local