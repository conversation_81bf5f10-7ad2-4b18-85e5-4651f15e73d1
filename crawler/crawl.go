package crawler

import (
	"log"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"github.com/amankumarsingh77/search_engine/models"
	"github.com/amankumarsingh77/search_engine/pkg"
	"github.com/gocolly/colly"
)

type crawler struct {
	collector *colly.Collector
	frontier  URLFrontier
	db        DB
	logger    *log.Logger
}

type WebCrawler interface {
	Process(url string) (*models.WebPage, error)
}

func NewCrawler(collector *colly.Collector, frontier URLFrontier, db DB, logger *log.Logger) WebCrawler {
	return &crawler{
		collector: collector,
		frontier:  frontier,
		db:        db,
		logger:    logger,
	}
}

func (c *crawler) Process(url string) (*models.WebPage, error) {
	pageData := models.WebPage{}
	//collector := c.collector.Clone()
	c.collector.OnHTML("html", func(e *colly.HTMLElement) {
		doc := e.DOM
		pageURL := e.Request.URL.String()

		title := strings.TrimSpace(doc.Find("title").Text())
		description := strings.TrimSpace(doc.Find("meta[name='description']").AttrOr("content", ""))
		keywordsRaw := strings.TrimSpace(doc.Find("meta[name='keywords']").AttrOr("content", ""))
		var keywords []string
		if keywordsRaw != "" {
			keywords = strings.Split(keywordsRaw, ",")
			for i, k := range keywords {
				keywords[i] = strings.TrimSpace(k)
			}
		}

		var bodyTextBuilder strings.Builder
		doc.Find("h1, h2, h3, h4, h5, h6, p, span, div").Each(func(_ int, s *goquery.Selection) {
			if s.Is("script") || s.Is("style") {
				return
			}
			bodyTextBuilder.WriteString(strings.TrimSpace(s.Text()))
			bodyTextBuilder.WriteString("\n")
		})

		pageData = models.WebPage{
			URL:         pageURL,
			Title:       title,
			Description: description,
			Keywords:    keywords,
			BodyText:    bodyTextBuilder.String(),
		}

		docID, err := c.db.AddWebpage(pageData)
		if err != nil {
			log.Printf("Error saving webpage %s: %v", pageURL, err)
			return
		}

		tokens := normalizePageContent(pageData.Title + " " + pageData.Description + " " + pageData.BodyText)
		if len(tokens) > 0 {
			pkg.AddToIndexAndDocLength(pkg.GlobalRawTFData, pkg.GlobalDocLengths, tokens, docID)
		}

		doc.Find("a[href]").Each(func(_ int, s *goquery.Selection) {
			link := e.Request.AbsoluteURL(s.AttrOr("href", ""))
			if link != "" {
				if strings.HasSuffix(link, e.Request.URL.Scheme+"://"+e.Request.URL.Host) {
					pageData.InternalLinks = append(pageData.InternalLinks, link)
				} else {
					pageData.ExternalLinks = append(pageData.ExternalLinks, link)
				}
				// URLs will be added to the frontier by the worker after processing
			}
		})
	})

	c.collector.OnRequest(func(r *colly.Request) {
		log.Println("Visiting", r.URL.String())
	})

	c.collector.OnError(func(r *colly.Response, err error) {
		log.Printf("Error crawling %s: %v (Status: %d)", r.Request.URL.String(), err, r.StatusCode)
	})

	c.collector.OnScraped(func(r *colly.Response) {
		log.Println("Finished scraping", r.Request.URL.String())
	})

	err := c.collector.Visit(url)
	if err != nil {
		log.Printf("Error initiating visit to %s: %v", url, err)
	}

	c.collector.Wait()
	return &pageData, nil
}
